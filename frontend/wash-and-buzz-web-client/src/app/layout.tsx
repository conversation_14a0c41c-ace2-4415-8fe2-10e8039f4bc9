import NextAuthProvider from '@/authContext/NextAuthProvider'
import { Bootstrap } from '@/components/bootstrap/Bootstrap'
import { Idbinit } from '@/components/idbInit/idbinit'
import IdleTimerComponent from '@/components/idleTimerComponent/IdleTimerComponent'

import ThemeProvider from '@/providers/theme-provider'
import Providers from '@/store/provider'
import appConfig from '@/utils/app-config.json'
import type { Metadata } from 'next'
import 'react-date-range/dist/styles.css' // main css file
import 'react-date-range/dist/theme/default.css' // theme css file
import 'react-responsive-modal/styles.css'
import 'slick-carousel/slick/slick-theme.css'
import 'slick-carousel/slick/slick.css'
import '@/styles/category.css'
import '@/styles/checkPaymentModalOption.css'
import '@/styles/commonPaddingMargin.css'
import '@/styles/countryInput.css'
import '@/styles/arBilling-table.css'
import '@/styles/current-ticket.css'
import '@/styles/customDropdown.css'
import '@/styles/customerTable.css'
import '@/styles/arBilling.css'
import '@/styles/account-invoice-table.css'
import '@/styles/customInputMask.css'
import '@/styles/dateRanger.css'
import '@/styles/generalDisplay.css'
import '@/styles/mediaQueries.css'
import '@/styles/mediaQueries/variant-box.mediaQueries.css'
import '@/styles/mediaQueries/item-library.mediaQueries.css'
import '@/styles/my-team.css'
import '@/styles/paymentTerminal.css'
import '@/styles/quick-drop-of.css'
import '@/styles/phoneKeyPad.css'
import '@/styles/popupModal.css'
import '@/styles/priceAdjustment.css'
import '@/styles/printers.css'
import '@/styles/profile.css'
import '@/styles/quantity-modal.css'
import '@/styles/reports.css'
import '@/styles/table.css'
import '@/styles/ticketFilters.css'
import '@/styles/ticketList.css'
import '@/styles/ticketTable.css'
import '@/styles/topbar.css'
import '@/styles/upchargeTable.css'
import '@/styles/variant-box.css'
import '@/styles/variants.css'
import '@/styles/customToastify.css'
import '@/styles/item-library-tab.css'
import '@/app/globals.css'
import '@/styles/fulfillment.css'
import '@/styles/item-library-table.css'
import '@/styles/home-page.css'
import '@/styles/phone-keypad.css'
import '@/styles/assign-rack-table.css'
import '@/styles/itemTransaction.css'
import '@/styles/pwa-tab-manager.css'
import Toast from '@/components/core/Toast/CustomToast'
export const metadata: Metadata = {
  title: appConfig.APP_TITLE,
  description: appConfig.APP_DESCRIPTION,
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'WashAndBuzz',
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: 'website',
    siteName: 'WashAndBuzz',
    title: appConfig.APP_TITLE,
    description: appConfig.APP_DESCRIPTION,
  },
  twitter: {
    card: 'summary',
    title: appConfig.APP_TITLE,
    description: appConfig.APP_DESCRIPTION,
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '16x16 32x32 48x48', type: 'image/x-icon' },
      { url: '/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icon-256x256.png', sizes: '256x256', type: 'image/png' },
      { url: '/icon-384x384.png', sizes: '384x384', type: 'image/png' },
      { url: '/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  themeColor: '#3c8dbc',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <ThemeProvider>
        <head>
          {/* Additional PWA Meta Tags not covered by metadata export */}
          <meta name="application-name" content="WashAndBuzz" />
          <meta name="mobile-web-app-capable" content="yes" />
          <meta name="msapplication-config" content="/browserconfig.xml" />
          <meta name="msapplication-TileColor" content="#3c8dbc" />
          <meta name="msapplication-tap-highlight" content="no" />

          {/* Additional PWA Icons */}
          <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#3c8dbc" />

          {/* Preconnect for performance */}
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link
            rel="preconnect"
            href="https://fonts.gstatic.com"
            crossOrigin=""
          />

          {/* Fonts */}
          <link
            href="https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&family=Inter:wght@100..900&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap"
            rel="stylesheet"
          />

          {/* Bootstrap CSS */}
          <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
            integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
            crossOrigin=""
          />
        </head>
        <body>
          <Providers>
            <NextAuthProvider>
              <Idbinit />
              <Bootstrap />
              <IdleTimerComponent />
              {children}
            </NextAuthProvider>
          </Providers>
          <Toast limit={1} />
        </body>
      </ThemeProvider>
    </html>
  )
}
