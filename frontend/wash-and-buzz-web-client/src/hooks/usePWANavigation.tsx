'use client'

import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { isPWA } from '@/utils/pwaNavigation'
import { addTab, PWATab } from '@/store/actions/pwaTabManager.action'

/**
 * Enhanced PWA Navigation Hook with Tab Manager
 * Provides PWA functionality with tab management for settings navigation
 */
// eslint-disable-next-line valid-jsdoc
export const usePWANavigation = () => {
  const router = useRouter()
  const dispatch = useDispatch()

  /**
   * Generate a unique tab ID
   */
  const generateTabId = useCallback(() => {
    return `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }, [])

  /**
   * Get page title from URL path
   */
  const getPageTitle = useCallback((path: string) => {
    const pathSegments = path.split('/').filter(Boolean)
    if (pathSegments.length === 0) return 'Home'

    // Convert path segments to readable titles
    const lastSegment = pathSegments[pathSegments.length - 1]
    return lastSegment
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }, [])

  /**
   * Open settings page with appropriate behavior
   * In PWA mode, opens as a new tab in the tab manager
   * In browser mode, opens in a new browser tab
   */
  const openSettings = useCallback(
    (settingsPath: string, title?: string) => {
      const runningAsPWA = isPWA()

      // Alert for testing - will remove this
      alert(`PWA Detection: ${runningAsPWA ? 'PWA Mode' : 'Browser Mode'}`)

      if (runningAsPWA) {
        // In PWA mode, use tab manager
        const tabTitle = title || getPageTitle(settingsPath)
        const tabId = generateTabId()

        const newTab: PWATab = {
          id: tabId,
          title: tabTitle,
          url: settingsPath,
          isActive: true,
          isClosable: true,
        }

        dispatch(addTab(newTab))
        router.push(settingsPath)
      } else {
        // In browser mode, open in new tab as originally intended
        window.open(settingsPath, '_blank', 'noopener,noreferrer')
      }
    },
    [router, dispatch, generateTabId, getPageTitle]
  )

  /**
   * Create a main tab for the current page
   * Used to initialize the tab manager with the current page
   */
  const createMainTab = useCallback(
    (currentPath: string, title?: string) => {
      const runningAsPWA = isPWA()

      if (runningAsPWA) {
        const tabTitle = title || getPageTitle(currentPath) || 'Main'
        const tabId = generateTabId()

        const mainTab: PWATab = {
          id: tabId,
          title: tabTitle,
          url: currentPath,
          isActive: true,
          isClosable: true, // All tabs should be closable
        }

        dispatch(addTab(mainTab))
      }
    },
    [dispatch, generateTabId, getPageTitle]
  )

  /**
   * Check if currently running as PWA
   */
  const isRunningAsPWA = useCallback(() => {
    return isPWA()
  }, [])

  return {
    openSettings,
    createMainTab,
    isRunningAsPWA,
    // Convenience methods
    isPWA: isRunningAsPWA,
  }
}

export default usePWANavigation
