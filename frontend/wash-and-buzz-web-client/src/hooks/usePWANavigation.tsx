'use client'

import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { isPWA } from '@/utils/pwaNavigation'

/**
 * Simplified PWA Navigation Hook
 * Provides essential PWA functionality for settings navigation
 */
// eslint-disable-next-line valid-jsdoc
export const usePWANavigation = () => {
  const router = useRouter()

  /**
   * Open settings page with appropriate behavior
   * Handles the specific case of settings pages that should open in new tabs
   */
  const openSettings = useCallback(
    (settingsPath: string) => {
      const runningAsPWA = isPWA()

      if (runningAsPWA) {
        // In PWA mode, navigate within the same context
        router.push(settingsPath)
      } else {
        // In browser mode, open in new tab as originally intended
        window.open(settingsPath, '_blank', 'noopener,noreferrer')
      }
    },
    [router]
  )

  /**
   * Check if currently running as PWA
   */
  const isRunningAsPWA = useCallback(() => {
    return isPWA()
  }, [])

  return {
    openSettings,
    isRunningAsPWA,
    // Convenience methods
    isPWA: isRunningAsPWA,
  }
}

export default usePWANavigation
