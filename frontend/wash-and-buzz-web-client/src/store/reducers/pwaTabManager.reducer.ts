import { createReducer } from '@reduxjs/toolkit'
import {
  PWATab,
  addTab,
  removeTab,
  setActiveTab,
  updateTab,
  closeAllTabs,
  setTabManagerVisible,
} from '../actions/pwaTabManager.action'

export interface PWATabManagerState {
  tabs: PWATab[]
  activeTabId: string | null
  isVisible: boolean
}

const initialState: PWATabManagerState = {
  tabs: [],
  activeTabId: null,
  isVisible: false,
}

const pwaTabManagerReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(addTab, (state, action) => {
      const newTab = action.payload
      
      // Check if tab with same URL already exists
      const existingTab = state.tabs.find(tab => tab.url === newTab.url)
      
      if (existingTab) {
        // If tab exists, just make it active
        state.activeTabId = existingTab.id
        state.tabs = state.tabs.map(tab => ({
          ...tab,
          isActive: tab.id === existingTab.id
        }))
      } else {
        // Add new tab and make it active
        state.tabs = state.tabs.map(tab => ({ ...tab, isActive: false }))
        state.tabs.push({ ...newTab, isActive: true })
        state.activeTabId = newTab.id
      }
      
      // Show tab manager if more than one tab
      state.isVisible = state.tabs.length > 1
    })
    
    .addCase(removeTab, (state, action) => {
      const tabIdToRemove = action.payload
      const tabIndex = state.tabs.findIndex(tab => tab.id === tabIdToRemove)
      
      if (tabIndex === -1) return
      
      // If removing active tab, activate another tab
      if (state.activeTabId === tabIdToRemove) {
        if (state.tabs.length > 1) {
          // Activate the next tab, or previous if it's the last tab
          const nextIndex = tabIndex < state.tabs.length - 1 ? tabIndex + 1 : tabIndex - 1
          const nextTab = state.tabs[nextIndex]
          state.activeTabId = nextTab.id
          state.tabs = state.tabs.map(tab => ({
            ...tab,
            isActive: tab.id === nextTab.id
          }))
        } else {
          state.activeTabId = null
        }
      }
      
      // Remove the tab
      state.tabs = state.tabs.filter(tab => tab.id !== tabIdToRemove)
      
      // Hide tab manager if only one or no tabs left
      state.isVisible = state.tabs.length > 1
    })
    
    .addCase(setActiveTab, (state, action) => {
      const tabId = action.payload
      state.activeTabId = tabId
      state.tabs = state.tabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId
      }))
    })
    
    .addCase(updateTab, (state, action) => {
      const { id, updates } = action.payload
      const tabIndex = state.tabs.findIndex(tab => tab.id === id)
      
      if (tabIndex !== -1) {
        state.tabs[tabIndex] = { ...state.tabs[tabIndex], ...updates }
      }
    })
    
    .addCase(closeAllTabs, (state) => {
      state.tabs = []
      state.activeTabId = null
      state.isVisible = false
    })
    
    .addCase(setTabManagerVisible, (state, action) => {
      state.isVisible = action.payload
    })
})

export default pwaTabManagerReducer
