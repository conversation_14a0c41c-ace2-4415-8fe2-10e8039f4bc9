import categoryDataReducer from '@/store/reducers/category.reducer'
import commonDataReducer from '@/store/reducers/common.reducer'
import currentTicketStateReducer from '@/store/reducers/currentTicketState.reducer'
import customersDataReducer from '@/store/reducers/customer.reducer'
import customerTicketsDataReducer from '@/store/reducers/customerTickets.reducer'
import userFetchDataReducer from '@/store/reducers/fetchUser.reducer'
import itemDataReducer from '@/store/reducers/item.reducer'
import itemPriceListReducer from '@/store/reducers/itemPriceList.reducer'
import { loginReducer } from '@/store/reducers/login.reducer'
import { logoutReducer } from '@/store/reducers/logout.reducer'
import paymentTerminalDataReducer from '@/store/reducers/paymentTerminal.reducer'
import PermissionDataReducer from '@/store/reducers/permission.reducer'
import PositionDataReducer from '@/store/reducers/position.reducer'
import priceAdjustmentDataReducer from '@/store/reducers/priceAdjustment.reducer'
import priceIncentiveDataReducer from '@/store/reducers/priceIncentive.reducer'
import printersDataReducer from '@/store/reducers/printers.reducer'
import productDataReducer from '@/store/reducers/product.reducer'
import salesReportsDataReducer from '@/store/reducers/salesReports.reducer'
import storeDataReducer from '@/store/reducers/store.reducer'
import taxSaleDataReducer from '@/store/reducers/taxSale.reducer'
import TeamMemberDataReducer from '@/store/reducers/teamMember.reducer'
import ticketSettingsDataReducer from '@/store/reducers/ticketSettings.reducer'
import ticketsDataReducer from '@/store/reducers/tickets.reducer'
import userDataReducer from '@/store/reducers/user.reducers'
import variantsDataReducer from '@/store/reducers/variants.reducer'
import { combineReducers } from 'redux'
import arBillingDataReducer from '@/store/reducers/arBilling.reducer'
import assignRackDataReducer from '@/store/reducers/assignRack.reducer'
import pwaTabManagerReducer from '@/store/reducers/pwaTabManager.reducer'

const rootReducer = combineReducers({
  userFetchData: userFetchDataReducer,
  productData: productDataReducer,
  userData: userDataReducer,
  storeData: storeDataReducer,
  itemData: itemDataReducer,
  categoryData: categoryDataReducer,
  itemPriceListData: itemPriceListReducer,
  variantsData: variantsDataReducer,
  priceAdjustmentData: priceAdjustmentDataReducer,
  logoutUserData: logoutReducer,
  loginUserData: loginReducer,
  priceIncentiveData: priceIncentiveDataReducer,
  taxSaleData: taxSaleDataReducer,
  ticketSettingsData: ticketSettingsDataReducer,
  ticketsData: ticketsDataReducer,
  currentTicketState: currentTicketStateReducer,
  printersData: printersDataReducer,
  customerData: customersDataReducer,
  positionData: PositionDataReducer,
  paymentTerminalData: paymentTerminalDataReducer,
  permissionsData: PermissionDataReducer,
  teamMemberData: TeamMemberDataReducer,
  salesReportsData: salesReportsDataReducer,
  commonData: commonDataReducer,
  customerTicketsData: customerTicketsDataReducer,
  arBillingData: arBillingDataReducer,
  assignRackData: assignRackDataReducer,
  pwaTabManagerData: pwaTabManagerReducer,
  // other reducers...
})

export default rootReducer
