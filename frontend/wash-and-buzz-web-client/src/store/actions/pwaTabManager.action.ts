import { createAction } from '@reduxjs/toolkit'

export interface PWATab {
  id: string
  title: string
  url: string
  isActive: boolean
  isClosable: boolean
}

// Action types
export const ADD_TAB = 'ADD_TAB'
export const REMOVE_TAB = 'REMOVE_TAB'
export const SET_ACTIVE_TAB = 'SET_ACTIVE_TAB'
export const UPDATE_TAB = 'UPDATE_TAB'
export const CLOSE_ALL_TABS = 'CLOSE_ALL_TABS'

// Action creators
export const addTab = createAction<PWATab>(ADD_TAB)
export const removeTab = createAction<string>(REMOVE_TAB)
export const setActiveTab = createAction<string>(SET_ACTIVE_TAB)
export const updateTab = createAction<{ id: string; updates: Partial<PWATab> }>(
  UPDATE_TAB
)
export const closeAllTabs = createAction(CLOSE_ALL_TABS)
