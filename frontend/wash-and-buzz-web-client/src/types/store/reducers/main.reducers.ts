import { CategoryDataState } from '@/types/store/reducers/category.reducers'
import { CommonDataState } from '@/types/store/reducers/common.reducers'
import { currentTicketStateData } from '@/types/store/reducers/currentTicketState.reducer'
import { CustomerDataState } from '@/types/store/reducers/customer.reducer'
import { ItemData } from '@/types/store/reducers/item.reducers'
import { ItemPriceListData } from '@/types/store/reducers/itemPriceList.reducer'
import { LoginState } from '@/types/store/reducers/login.reducer'
import { LogoutState } from '@/types/store/reducers/logout.reducer'
import { PaymentTerminalDataState } from '@/types/store/reducers/paymentTerminal.reducers'
import { PermissionData } from '@/types/store/reducers/permission.reducer'
import { PositionData } from '@/types/store/reducers/position.reducer'
import { PriceAdjustmentData } from '@/types/store/reducers/priceAdjustment.reducers'
import { PriceIncentiveData } from '@/types/store/reducers/priceIncentive.reducers'
import { PrintersDataState } from '@/types/store/reducers/printers.reducers'
import { ProductDataState } from '@/types/store/reducers/product.reducers'
import { SalesReportsDataState } from '@/types/store/reducers/salesReports.reducers'
import { StoreData } from '@/types/store/reducers/store.reducers'
import { TaxSaleData } from '@/types/store/reducers/taxSale.reducers'
import { TeamMemberData } from '@/types/store/reducers/teamMember.reducer'
import { TicketSettingsData } from '@/types/store/reducers/ticketSetting.reducers'
import { TicketsData } from '@/types/store/reducers/tickets.reducer'
import { UserData, UserDataState } from '@/types/store/reducers/user.reducers'
import { VariantsData } from '@/types/store/reducers/variants.reducer'
import { CustomerTicketsState } from '@/types/store/reducers/customerTickets.reducers'
import { ArBillingDataState } from '@/types/store/reducers/arBilling.reducer'
import { AssignRackDataState } from './assignRack.reducer'
import { PWATabManagerState } from '@/store/reducers/pwaTabManager.reducer'

export interface MainStoreType {
  userFetchData: UserDataState
  productData: ProductDataState
  userData: UserData
  storeData: StoreData
  itemData: ItemData
  itemPriceListData: ItemPriceListData
  categoryData: CategoryDataState
  variantsData: VariantsData
  priceAdjustmentData: PriceAdjustmentData
  logoutUserData: LogoutState
  loginUserData: LoginState
  priceIncentiveData: PriceIncentiveData
  taxSaleData: TaxSaleData
  ticketSettingsData: TicketSettingsData
  ticketsData: TicketsData
  currentTicketState: currentTicketStateData
  printersData: PrintersDataState
  customerData: CustomerDataState
  paymentTerminalData: PaymentTerminalDataState
  positionData: PositionData
  permissionsData: PermissionData
  teamMemberData: TeamMemberData
  salesReportsData: SalesReportsDataState
  commonData: CommonDataState
  customerTicketsData: CustomerTicketsState
  arBillingData: ArBillingDataState
  assignRackData: AssignRackDataState
  pwaTabManagerData: PWATabManagerState
}
