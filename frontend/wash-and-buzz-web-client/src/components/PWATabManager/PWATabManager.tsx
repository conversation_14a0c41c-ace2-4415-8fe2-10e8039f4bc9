'use client'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { isPWA } from '@/utils/pwaNavigation'
import PWATabBar from './PWATabBar'

interface PWATabManagerProps {
  children: React.ReactNode
}

const PWATabManager: React.FC<PWATabManagerProps> = ({ children }) => {
  const { tabs } = useSelector(
    (state: MainStoreType) => state.pwaTabManagerData
  )
  const [isPWAMode, setIsPWAMode] = useState(false)

  useEffect(() => {
    setIsPWAMode(isPWA())
  }, [])

  return (
    <>
      {isPWAMode && tabs.length > 0 && <PWATabBar />}
      {children}
    </>
  )
}

export default PWATabManager
