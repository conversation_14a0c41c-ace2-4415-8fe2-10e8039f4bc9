'use client'
import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { isPWA } from '@/utils/pwaNavigation'
import PWATabBar from './PWATabBar'

interface PWATabManagerProps {
  children: React.ReactNode
}

const PWATabManager: React.FC<PWATabManagerProps> = ({ children }) => {
  const { isVisible } = useSelector(
    (state: MainStoreType) => state.pwaTabManagerData
  )
  
  const [isPWAMode, setIsPWAMode] = React.useState(false)

  useEffect(() => {
    // Check if running as PWA
    setIsPWAMode(isPWA())
  }, [])

  // Only show tab manager in PWA mode
  if (!isPWAMode || !isVisible) {
    return <>{children}</>
  }

  return (
    <>
      <PWATabBar />
      {children}
    </>
  )
}

export default PWATabManager
