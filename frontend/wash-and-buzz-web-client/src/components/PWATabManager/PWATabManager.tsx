'use client'
import React from 'react'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import PWATabBar from './PWATabBar'

interface PWATabManagerProps {
  children: React.ReactNode
}

const PWATabManager: React.FC<PWATabManagerProps> = ({ children }) => {
  const { tabs } = useSelector(
    (state: MainStoreType) => state.pwaTabManagerData
  )

  return (
    <>
      {tabs.length > 0 && <PWATabBar />}
      {children}
    </>
  )
}

export default PWATabManager
