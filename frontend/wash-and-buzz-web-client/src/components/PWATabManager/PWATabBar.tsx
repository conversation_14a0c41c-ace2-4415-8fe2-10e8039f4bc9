'use client'
import React from 'react'
import { Box, Flex } from 'theme-ui'
import { useSelector, useDispatch } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { setActiveTab, removeTab } from '@/store/actions/pwaTabManager.action'
import PWATab from './PWATab'
import { useRouter } from 'next/navigation'

const PWATabBar: React.FC = () => {
  const dispatch = useDispatch()
  const router = useRouter()
  const { tabs, activeTabId } = useSelector(
    (state: MainStoreType) => state.pwaTabManagerData
  )

  const handleTabClick = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId)
    if (tab) {
      dispatch(setActiveTab(tabId))
      router.push(tab.url)
    }
  }

  const handleTabClose = (tabId: string) => {
    const tabToClose = tabs.find(t => t.id === tabId)
    if (!tabToClose) return

    dispatch(removeTab(tabId))

    // If we're closing the active tab, navigate to the new active tab
    if (tabId === activeTabId) {
      const remainingTabs = tabs.filter(t => t.id !== tabId)
      if (remainingTabs.length > 0) {
        const newActiveTab = remainingTabs[0]
        router.push(newActiveTab.url)
      }
    }
  }

  if (tabs.length <= 1) {
    return null
  }

  return (
    <Box
      className="pwa-tab-bar"
      sx={{
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #ddd',
        padding: '0 16px',
        overflowX: 'auto',
        whiteSpace: 'nowrap',
        '&::-webkit-scrollbar': {
          height: '4px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: '#f1f1f1',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: '#c1c1c1',
          borderRadius: '2px',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          backgroundColor: '#a8a8a8',
        },
      }}
    >
      <Flex
        sx={{
          alignItems: 'flex-end',
          gap: '0px',
          paddingTop: '8px',
          minHeight: '40px',
        }}
      >
        {tabs.map((tab) => (
          <PWATab
            key={tab.id}
            tab={tab}
            onTabClick={handleTabClick}
            onTabClose={handleTabClose}
            isActive={tab.id === activeTabId}
          />
        ))}
      </Flex>
    </Box>
  )
}

export default PWATabBar
