'use client'
import React, { useEffect } from 'react'
import { Box, Button, Text } from 'theme-ui'
import { usePWANavigation } from '@/hooks/usePWANavigation'
import { usePathname } from 'next/navigation'

const PWATabManagerTest: React.FC = () => {
  const { openSettings, createMainTab, isRunningAsPWA } = usePWANavigation()
  const pathname = usePathname()

  useEffect(() => {
    // Create main tab when component mounts in PWA mode
    if (isRunningAsPWA()) {
      createMainTab(pathname, 'Main Dashboard')
    }
  }, [pathname, createMainTab, isRunningAsPWA])

  const handleOpenSettings = () => {
    openSettings('/settings/store-details', 'Store Details')
  }

  const handleOpenProfile = () => {
    openSettings('/profile', 'Profile Settings')
  }

  const handleOpenReports = () => {
    openSettings('/reports', 'Reports')
  }

  return (
    <Box
      sx={{
        padding: '20px',
        maxWidth: '600px',
        margin: '0 auto',
      }}
    >
      <Text variant="Primary28Demi116" sx={{ marginBottom: '20px' }}>
        PWA Tab Manager Test
      </Text>
      
      <Text variant="Primary16Regular122" sx={{ marginBottom: '20px' }}>
        {isRunningAsPWA() 
          ? '✅ Running as PWA - Tab manager is active' 
          : '❌ Running in browser - Links will open in new tabs'
        }
      </Text>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <Button
          variant="primary"
          onClick={handleOpenSettings}
          sx={{ padding: '12px 20px' }}
        >
          Open Store Details Settings
        </Button>

        <Button
          variant="primary"
          onClick={handleOpenProfile}
          sx={{ padding: '12px 20px' }}
        >
          Open Profile Settings
        </Button>

        <Button
          variant="primary"
          onClick={handleOpenReports}
          sx={{ padding: '12px 20px' }}
        >
          Open Reports
        </Button>
      </Box>

      <Box sx={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <Text variant="Primary16Medium122" sx={{ marginBottom: '10px' }}>
          Instructions:
        </Text>
        <Text variant="Primary14Regular122" sx={{ lineHeight: '1.5' }}>
          1. <strong>In Browser Mode:</strong> Clicking buttons will open links in new browser tabs<br/>
          2. <strong>In PWA Mode:</strong> Clicking buttons will create new tabs in the PWA tab manager<br/>
          3. <strong>To test PWA mode:</strong> Install the app using the browser's install prompt, then open the installed app
        </Text>
      </Box>
    </Box>
  )
}

export default PWATabManagerTest
