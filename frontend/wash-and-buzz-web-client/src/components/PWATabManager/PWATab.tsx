'use client'
import React from 'react'
import { Box, Text } from 'theme-ui'
import Image from 'next/image'
import { PWATab as PWATabType } from '@/store/actions/pwaTabManager.action'
import closeIcon from '@/../public/images/cross-red-icon.svg'

interface PWATabProps {
  tab: PWATabType
  onTabClick: (tabId: string) => void
  onTabClose: (tabId: string) => void
  isActive: boolean
}

const PWATab: React.FC<PWATabProps> = ({
  tab,
  onTabClick,
  onTabClose,
  isActive,
}) => {
  const handleTabClick = (e: React.MouseEvent) => {
    e.preventDefault()
    onTabClick(tab.id)
  }

  const handleCloseClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (tab.isClosable) {
      onTabClose(tab.id)
    }
  }

  return (
    <Box
      className={`pwa-tab ${isActive ? 'active' : ''}`}
      onClick={handleTabClick}
      sx={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: isActive ? 'white' : '#f5f5f5',
        border: '1px solid #ddd',
        borderBottom: isActive ? 'none' : '1px solid #ddd',
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        cursor: 'pointer',
        minWidth: '120px',
        maxWidth: '200px',
        position: 'relative',
        marginRight: '2px',
        '&:hover': {
          backgroundColor: isActive ? 'white' : '#e9e9e9',
        },
        '&.active': {
          zIndex: 1,
          borderColor: '#297189',
          borderBottomColor: 'white',
        },
      }}
    >
      <Text
        variant="Primary14Regular122"
        sx={{
          flex: 1,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          color: isActive ? '#297189' : '#303030',
          fontWeight: isActive ? '500' : '400',
          marginRight: tab.isClosable ? '8px' : '0',
        }}
      >
        {tab.title}
      </Text>

      {tab.isClosable && (
        <Box
          onClick={handleCloseClick}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            },
          }}
        >
          <Image
            src={closeIcon}
            alt="Close tab"
            width={12}
            height={12}
            style={{ opacity: 0.6 }}
          />
        </Box>
      )}
    </Box>
  )
}

export default PWATab
