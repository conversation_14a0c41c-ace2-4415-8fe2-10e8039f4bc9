'use client'
import HomeComponent from '@/components/homePage/HomeComponent'
import CustomerMenu from '@/components/layouts/CustomerMenu'
import PosFooter from '@/components/layouts/PosFooter'
import { PosSideMenu } from '@/components/layouts/PosSideMenu'
import { PrimaryTopbar } from '@/components/layouts/Topbar'
import PWATabManager from '@/components/PWATabManager/PWATabManager'
import withSingletonBehavior from '@/components/singletonBehavior/SingletonBehavior'
import {
  setOpenLeaveConfirmationModal,
  setRequestedAction,
  setRequestedRoute,
} from '@/store/actions/currentTicketState.action'
import {
  getSelectedCustomersData,
  resetSelectedCustomer,
  setSelectedCustomersData,
} from '@/store/actions/customer.action'
import { getStoreDataRequest } from '@/store/actions/store.actions'
import { clearCurrentTicket } from '@/store/actions/ticket.action'
import { PrinterData } from '@/types/module/printersModule'
import { RequestedAction } from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import {
  computeMenuList,
  getAppRouteBasedOnProductValue,
  getDashboardRouteBasedOnProduct,
} from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import { customerMenuItems } from '@/utils/sideMenuData'
import { usePathname, useRouter } from 'next/navigation'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

const DefaultPage: FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useDispatch()
  const route = useRouter()
  const pathname = usePathname()
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const { data } = useSelector((state: MainStoreType) => state.printersData)
  const customerList = useSelector((state: MainStoreType) => state.customerData)
  const currentTicketState = useSelector(
    (state: MainStoreType) => state.ticketsData
  )
  const [selectedPrinterData, setSelectedPrinterData] = useState<
    PrinterData[] | null
  >(null)
  const [activeDrawer, setActiveDrawer] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState('')

  const handleTabChange = useCallback((tabName: string) => {
    setActiveTab(tabName)
  }, [])

  useEffect(() => {
    handleTabChange('')
  }, [handleTabChange, customerList.selectedCustomer])

  useEffect(() => {
    if (data && data?.length > 0) {
      const selectedPrinter = data.filter(
        (printer) =>
          printer.enabled && printer.usage?.[0]?.enabled && printer.isSelected
      )

      setSelectedPrinterData(selectedPrinter || null)
    }
  }, [data])

  useEffect(() => {
    if (storeValue?.authorizedStores === null) {
      dispatch(getStoreDataRequest())
    }
  }, [dispatch, storeValue?.authorizedStores])

  const handleHomeClick = () => {
    if (currentTicketState.currentTicket?.isEdit) {
      dispatch(setRequestedRoute(appRoutes.homePage))
      dispatch(setOpenLeaveConfirmationModal(true))
      dispatch(setRequestedAction(RequestedAction.RESET_CUSTOMER_DATA))
      return
    }
    if (customerList?.selectedCustomer) {
      dispatch(resetSelectedCustomer())
      if (storeValue?.data?.posProductType) {
        route.push(
          getAppRouteBasedOnProductValue(storeValue?.data?.posProductType)
        )
      }
    } else {
      const storeCustomerId = customerList.data?.[0]?.storeCustomerId as number
      dispatch(
        getSelectedCustomersData({ storeCustomerId }, () => {
          dispatch(clearCurrentTicket())
          dispatch(
            setSelectedCustomersData(
              {
                storeCustomerId,
                isSelectedCustomer: 1,
              },
              customerList?.selectedCustomer
                ? Number(customerList?.selectedCustomer?.storeCustomerId)
                : null,
              (res) => {
                if (res && storeValue?.data?.posProductType) {
                  route.push(
                    getDashboardRouteBasedOnProduct(
                      storeValue?.data?.posProductType
                    )
                  )
                }
              }
            )
          )
        })
      )
    }
  }

  const menuList = useMemo(() => {
    return storeValue && storeValue.data?.posProductType
      ? computeMenuList({
          menuItems: customerMenuItems,
          posProductType: storeValue.data?.posProductType as number,
          selectedCustomerData: customerList?.selectedCustomer,
        })
      : []
  }, [storeValue, customerList?.selectedCustomer])

  return (
    <PWATabManager>
      {!(pathname === appRoutes.homePage) && <PrimaryTopbar />}
      {!(pathname === appRoutes.homePage) && (
        <PosSideMenu>
          {customerList?.selectedCustomer && (
            <CustomerMenu
              menuList={menuList}
              handleHomeClick={handleHomeClick}
              activeDrawer={activeDrawer}
              selectedPrinterData={selectedPrinterData}
              setActiveDrawer={setActiveDrawer}
            />
          )}
          {children}
        </PosSideMenu>
      )}
      {pathname === appRoutes.homePage && <HomeComponent />}
      {!(pathname === appRoutes.homePage) && customerList?.selectedCustomer && (
        <PosFooter
          menuList={menuList}
          selectedPrinterData={selectedPrinterData}
          activeDrawer={activeDrawer}
          setActiveDrawer={setActiveDrawer}
          handleTabChange={handleTabChange}
          activeTab={activeTab}
        />
      )}
    </PWATabManager>
  )
}

export default withSingletonBehavior(DefaultPage)
