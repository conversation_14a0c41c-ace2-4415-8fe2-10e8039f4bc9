'use client'
import { MenuItemProps, SidebarDataProps } from '@/types/module/sideMenuModule'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useState, FC } from 'react'
import { Box, Card, Text } from 'theme-ui'

export const SettingHomeMenu: FC<SidebarDataProps> = ({ menuItems }) => {
  const router = useRouter()
  const pathname = usePathname()
  const [buttonIndex, setButtonIndex] = useState<number | null>(null)

  const handleMenuItemClick = (
    link?: string,
    event?: React.MouseEvent<HTMLAnchorElement>
  ) => {
    if (link) {
      router.push(link)
    }
    if (event && event.currentTarget) {
      event.currentTarget.blur()
    }
  }

  return (
    <Card variant="settingSideMenu" className="custom-scroll">
      <Box
        sx={{ overflowY: 'auto' }}
        as="div"
        className="sideMenuItem setting-sidemenu-scroll-height "
      >
        {Array.isArray(menuItems) &&
          menuItems?.map((menuItem: MenuItemProps, index: number) => {
            const isActive = pathname === menuItem?.link
            return (
              <Link
                key={index}
                className="setting-sidenav-link align-items-center"
                href={menuItem?.link}
                onClick={(e) => {
                  setButtonIndex(null)
                  handleMenuItemClick(menuItem?.link, e)
                }}
                onMouseOver={() => {
                  setButtonIndex(index)
                }}
                onMouseOut={() => {
                  setButtonIndex(null)
                }}
              >
                <Image
                  height={20}
                  width={20}
                  src={`${isActive ? menuItem.selectedImageUrl : buttonIndex === index ? menuItem.selectedImageUrl : menuItem?.imageUrl}`}
                  alt={`${menuItem?.title}`}
                />
                <Text
                  className="long-text-ellipsis"
                  variant="Primary18Regular71"
                  sx={{
                    ml: '10px',
                    color: isActive ? '#297189' : '',
                    fontWeight: isActive ? '500' : '400',
                  }}
                >
                  {menuItem?.title}
                </Text>
              </Link>
            )
          })}
      </Box>
    </Card>
  )
}
