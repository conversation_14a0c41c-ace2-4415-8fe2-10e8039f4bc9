'use client'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box, Card, Flex, Text } from 'theme-ui'

import helpIcon from '../../../public/images/help-icon.svg'
import homeIcon from '../../../public/images/home-icon.svg'
import { ThemeButton } from '../core/Button/Button'
import { HelpModal } from '../core/PopupModals/HelpModal'

import { getItemPriceListData } from '@/store/actions/itemPriceList.action'
import { MenuItemProps, SubSideMenuProps } from '@/types/module/sideMenuModule'
import { ItemPriceListDataType } from '@/types/store/actions/itemPriceList.action'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { appRoutes } from '@/utils/routes'
import { translation } from '@/utils/translation'
import { Dispatch } from 'redux'

export const SubSideMenu: FC<SubSideMenuProps> = ({
  variant,
  menuItems,
  menuName,
}) => {
  const ItemDetails = useSelector((state: MainStoreType) => state.itemData)
  const storeData = useSelector((state: MainStoreType) => state.storeData)
  const itemPriceListData = useSelector(
    (state: MainStoreType) => state.itemPriceListData
  )
  const dispatch: Dispatch<ItemPriceListDataType> = useDispatch()
  const ItemData =
    Array.isArray(ItemDetails?.data) && ItemDetails?.data?.length === 0
  const router = useRouter()
  const pathname = usePathname()
  const [openModal, setOpenModal] = useState(false)

  const handleMenuItemClick = (
    link?: string,
    event?: React.MouseEvent<HTMLAnchorElement>
  ) => {
    if (link) {
      router.push(link)
    }
    if (event && event.currentTarget) {
      event.currentTarget.blur()
    }
  }
  useEffect(() => {
    if (ItemData && pathname.includes(translation.ITEMS_PRICE_LIST)) {
      setOpenModal(true)
    } else {
      setOpenModal(false)
    }
    // eslint-disable-next-line
  }, [ItemData, pathname])

  useEffect(() => {
    if (
      itemPriceListData.data === null &&
      pathname.includes(translation.ITEMS_PRICE_LIST)
    ) {
      dispatch(getItemPriceListData())
    }
    // eslint-disable-next-line
  }, [dispatch, pathname])

  return (
    <Card
      variant={variant}
      className="d-flex flex-column justify-content-between vh-100 custom-scroll"
    >
      <Box as="div">
        {menuName && (
          <Flex
            sx={{
              alignItems: 'center',
              cursor: 'pointer',
              py: '6px',
              mb: '60px',
            }}
            onClick={() => router.push(appRoutes.storeDetails)}
          >
            <Image width={20} height={20} src={homeIcon} alt="img" />
            <Box as="div">
              <Text
                as="p"
                sx={{ ml: '10px', textAlign: 'center' }}
                variant="Primary16Medium88"
              >
                {translation.HOME}
              </Text>
            </Box>
          </Flex>
        )}
        <Text as="p" px="4px" className="mb-40" variant="Primary22Demi90">
          {menuName}
        </Text>
        <Box className="sub-sidemenu-scroll-height overflow-y-auto overflow-x-hidden">
          <Box as="div" className="sideMenuItem">
            {Array.isArray(menuItems) &&
              menuItems
                ?.filter((val) =>
                  val.enabledInProduct?.includes(storeData.data?.posProductType)
                )
                ?.map((menuItem: MenuItemProps, index: number) => {
                  const isActive = pathname === menuItem?.link
                  return (
                    <Link
                      className="setting-subsidenav-link"
                      href={menuItem?.link}
                      key={index}
                      onClick={(e) => {
                        handleMenuItemClick(menuItem?.link, e)
                      }}
                    >
                      <Text
                        as="p"
                        variant="Primary18Regular71"
                        sx={{
                          color: isActive ? '#297189' : '',
                          fontWeight: isActive ? '500' : '400',
                          maxWidth: '22ch',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'wrap',
                        }}
                      >
                        {menuItem?.title}
                      </Text>
                    </Link>
                  )
                })}
          </Box>
        </Box>
      </Box>
      {menuName === 'Item Library' && (
        <ThemeButton
          sx={{ justifyContent: 'start !important', color: '#303030' }}
          className="cta-button col-3"
          variant="quaternaryBtn"
          text="Help"
          icon={helpIcon}
          onClick={() => setOpenModal(true)}
        />
      )}
      {menuName === 'Item Library' && (
        <HelpModal
          isOpen={openModal}
          onClose={() => setOpenModal(false)}
          closeIcon={''}
          title={translation?.HELP_ONBOARDING_PROCESS}
          modalContainer="help-modal"
          steps={[
            {
              title: translation.HELP_MODAL_ITEM_AND_PRICE_LIST_TITLE,
              description:
                translation.HELP_MODAL_ITEM_AND_PRICE_LIST_DESCRIPTION,
              linkText: translation.ITEM_AND_PRICE_LIST,
              linkPath: appRoutes.itemsPriceList,
            },
            {
              title: translation.HELP_MODAL_CATEGORY_TITLE,
              description: translation.HELP_MODAL_CATEGORY_DESCRIPTION,
              linkText: translation.CATEGORIES,
              linkPath: appRoutes.categories,
            },
            {
              title: translation.HELP_MODAL_VARIANT_TITLE,
              description: translation.HELP_MODAL_VARIANT_DESCRIPTION,
              linkText: translation.VARIANTS_TITLE,
              linkPath: appRoutes.variants,
            },
          ]}
        />
      )}
    </Card>
  )
}
