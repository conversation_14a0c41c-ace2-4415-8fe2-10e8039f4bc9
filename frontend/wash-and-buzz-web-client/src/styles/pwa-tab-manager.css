/* PWA Tab Manager Styles */

.pwa-tab-bar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pwa-tab {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border: 1px solid #dee2e6;
  border-bottom: none;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  margin-right: 2px;
  min-width: 120px;
  max-width: 200px;
  height: 32px;
  padding: 0 12px;
}

.pwa-tab:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.pwa-tab.active {
  background-color: #ffffff;
  border-color: #297189;
  border-bottom: 1px solid #ffffff;
  z-index: 2;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.pwa-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #ffffff;
  z-index: 3;
}

.pwa-tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 400;
  color: #303030;
  margin-right: 8px;
}

.pwa-tab.active .pwa-tab-title {
  color: #297189;
  font-weight: 500;
}

.pwa-tab-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.pwa-tab-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.pwa-tab.active .pwa-tab-close:hover {
  background-color: rgba(41, 113, 137, 0.1);
}

.pwa-tab-close img {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.pwa-tab-close:hover img {
  opacity: 0.8;
}

/* Scrollbar styling for tab bar */
.pwa-tab-bar::-webkit-scrollbar {
  height: 4px;
}

.pwa-tab-bar::-webkit-scrollbar-track {
  background-color: #f1f3f4;
  border-radius: 2px;
}

.pwa-tab-bar::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 2px;
}

.pwa-tab-bar::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* Firefox scrollbar */
.pwa-tab-bar {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f3f4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pwa-tab {
    min-width: 100px;
    max-width: 150px;
    padding: 0 8px;
  }
  
  .pwa-tab-title {
    font-size: 13px;
    margin-right: 6px;
  }
  
  .pwa-tab-close {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .pwa-tab {
    min-width: 80px;
    max-width: 120px;
    padding: 0 6px;
  }
  
  .pwa-tab-title {
    font-size: 12px;
    margin-right: 4px;
  }
}

/* Animation for tab transitions */
.pwa-tab {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles for accessibility */
.pwa-tab:focus {
  outline: 2px solid #297189;
  outline-offset: -2px;
}

.pwa-tab-close:focus {
  outline: 2px solid #297189;
  outline-offset: -2px;
}
