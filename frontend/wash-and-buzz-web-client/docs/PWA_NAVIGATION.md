# PWA Navigation Guide

This document explains how navigation works in the WashAndBuzz PWA, particularly for pages and sub-pages that previously used `target="_blank"`.

## Simplified Tab Management

The PWA now includes a **simple tab manager** that only activates when `_blank` links are opened in PWA mode. This provides a browser-like experience for managing multiple tabs without overwhelming the interface.

## Overview

The PWA navigation system automatically handles different navigation behaviors based on whether the app is running as an installed PWA or in a regular browser context.

## Key Components

### 1. PWA Navigation Utilities (`src/utils/pwaNavigation.ts`)

Core utilities that detect PWA context and handle navigation appropriately:

- `isPWA()` - Detects if app is running as installed PWA
- `navigatePWA()` - Smart navigation that adapts to PWA context
- `openPWA()` - PWA-aware replacement for `window.open()`
- `getLinkTarget()` - Returns appropriate link target based on context

### 2. PWA Navigation Hook (`src/hooks/usePWANavigation.tsx`)

React hook that provides PWA-aware navigation methods:

```tsx
const { openSettings, navigate, push, open, isRunningAsPWA } =
  usePWANavigation()
```

### 3. PWA Link Components (`src/components/PWALink/PWALink.tsx`)

React components for consistent PWA navigation:

- `PWALink` - General purpose PWA-aware link
- `PWASettingsLink` - Specifically for settings pages
- `PWAExternalLink` - For external URLs

## Navigation Behavior

### Settings Pages (Previously `target="_blank"`)

**Before:**

```tsx
window.open(appRoutes.storeDetails, '_blank')
```

**After:**

```tsx
const { openSettings } = usePWANavigation()
openSettings(appRoutes.storeDetails)
```

**Behavior:**

- **In PWA mode**: Opens in same app context (no new tab)
- **In browser mode**: Opens in new tab as before

### Regular Internal Navigation

**Using the hook:**

```tsx
const { navigate } = usePWANavigation()
navigate('/some-page', { openInNewTab: false })
```

**Using the component:**

```tsx
<PWALink href="/some-page">Navigate to Page</PWALink>
```

### External Links

**Using the hook:**

```tsx
const { navigate } = usePWANavigation()
navigate('https://external-site.com', { external: true })
```

**Using the component:**

```tsx
<PWAExternalLink href="https://external-site.com">
  External Site
</PWAExternalLink>
```

## Manifest Configuration

The `manifest.json` has been configured to handle new tab behavior:

```json
{
  "launch_handler": {
    "client_mode": "navigate-new"
  },
  "handle_links": "auto",
  "capture_links": "new-client-navigate",
  "scope": "/",
  "navigation_scope": "/"
}
```

## Implementation Examples

### 1. Settings Menu (Updated)

```tsx
// Before
onClick: () => window.open(appRoutes.storeDetails, '_blank')

// After
const { openSettings } = usePWANavigation()
onClick: () => openSettings(appRoutes.storeDetails)
```

### 2. Navigation Links

```tsx
// For settings pages that should adapt to PWA context
<PWASettingsLink href="/settings/store-details">
  Store Settings
</PWASettingsLink>

// For regular internal navigation
<PWALink href="/dashboard">
  Dashboard
</PWALink>

// For external links
<PWAExternalLink href="https://help.washandbuzz.com">
  Help Documentation
</PWAExternalLink>
```

### 3. Programmatic Navigation

```tsx
const { navigate, push, isRunningAsPWA } = usePWANavigation()

// Check if running as PWA
if (isRunningAsPWA()) {
  // PWA-specific behavior
  navigate('/settings', { openInNewTab: false })
} else {
  // Browser-specific behavior
  navigate('/settings', { openInNewTab: true })
}

// Or use the smart navigation that handles this automatically
push('/settings') // Automatically adapts to context
```

## Migration Guide

### Step 1: Replace `window.open()` calls

**Find:**

```tsx
window.open(url, '_blank')
```

**Replace with:**

```tsx
const { openSettings } = usePWANavigation()
openSettings(url)
```

### Step 2: Update Link Components

**Find:**

```tsx
<a href="/settings" target="_blank">
  Settings
</a>
```

**Replace with:**

```tsx
<PWASettingsLink href="/settings">Settings</PWASettingsLink>
```

### Step 3: Update Router Navigation

**Find:**

```tsx
router.push(url)
// when you want new tab behavior
```

**Replace with:**

```tsx
const { push } = usePWANavigation()
push(url, { openInNewTab: true })
```

## Testing

### PWA Mode Testing

1. Install the app as PWA
2. Navigate to settings - should open in same app context
3. Test app shortcuts - should work correctly

### Browser Mode Testing

1. Open app in regular browser
2. Navigate to settings - should open in new tab
3. Test all navigation flows

### Development Mode

- PWA status panel shows current context
- Install prompt appears when app is installable
- All navigation behaviors can be tested

## Benefits

1. **Consistent UX**: Navigation adapts to user's context
2. **Better PWA Experience**: No unwanted new tabs in PWA mode
3. **Backward Compatibility**: Maintains browser behavior when not installed
4. **Easy Migration**: Drop-in replacements for existing navigation
5. **Type Safety**: Full TypeScript support

## Troubleshooting

### Links not working in PWA

- Check if using PWA-aware components
- Verify manifest configuration
- Test in both PWA and browser modes

### New tabs opening in PWA when they shouldn't

- Use `openSettings()` instead of `navigate()` with `openInNewTab: true`
- Check PWA detection logic

### External links not opening

- Use `PWAExternalLink` component
- Or use `navigate()` with `external: true` option
